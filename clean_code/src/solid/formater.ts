import Circle from "./shapes/circle";
import Square from "./shapes/square";

export default class Formatter {
  toJson(shapes) {
    return JSON.stringify(shapes);
  }

  toCVS(shapes) {
    return shapes
      .map((shape) => {
        if (shape instanceof Circle) {
          return `Circle,${shape.getRadius()}`;
        } else if (shape instanceof Square) {
          return `Square,${shape.getLength()}`;
        }
      })
      .join("\n");
  }
}
