import AreaCalculator from "./areaCalcualtor";
import Circle from "./shapes/circle";
import Formatter from "./formater";
import Square from "./shapes/square";

const areaCalcualtor = new AreaCalculator();
const circle = new Circle(10);
const square = new Square(10);
const shapes = [circle, square];
const formater = new Formatter();
console.log(formater.toCVS(shapes));
console.log(formater.toJson(shapes));
const sum = areaCalcualtor.sum(shapes);
console.log(sum);
