import esbuild from "esbuild";

esbuild
  .build({
    entryPoints: ["src/solid/main.ts"],
    bundle: true,
    platform: "node",
    target: "node18",
    outfile: "dist/index.js",
    format: "cjs",
    sourcemap: true,
    tsconfig: "tsconfig.json",
    external: ["node:*"],
    resolveExtensions: [".ts"],
  })
  .then(() => console.log("Build succeeded"))
  .catch((error) => {
    console.error("Build failed:", error);
    process.exit(1);
  });
