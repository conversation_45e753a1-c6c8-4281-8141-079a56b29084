{"version": 3, "sources": ["../src/solid/areaCalcualtor.ts", "../src/solid/shapes/base/shape.ts", "../src/solid/shapes/circle.ts", "../src/solid/shapes/square.ts", "../src/solid/formater.ts", "../src/solid/main.ts"], "sourcesContent": ["export default class AreaCalculator {\n  sum(shapes) {\n    return shapes.reduce((a, b) => {\n      return a + b.area();\n    }, 0);\n  }\n}\n", "export default class Shape {\n  area() {\n    return 0;\n  }\n}\n", "import Shape from \"./base/shape\";\n\nexport default class Circle extends Shape {\n  constructor(radius) {\n    super();\n    this[\"#radius\"] = radius;\n  }\n\n  getRadius() {\n    return this[\"#radius\"];\n  }\n\n  area() {\n    return Math.PI * this.getRadius() * this.getRadius();\n  }\n}\n", "import Shape from \"./base/shape\";\n\nexport default class Square extends Shape {\n  constructor(length) {\n    super();\n    this[\"#length\"] = length;\n  }\n\n  getLength() {\n    return this[\"#length\"];\n  }\n\n  area() {\n    return this.getLength() * this.getLength();\n  }\n}\n", "import Circle from \"./shapes/circle\";\nimport Square from \"./shapes/square\";\n\nexport default class Formatter {\n  toJson(shapes) {\n    return JSON.stringify(shapes);\n  }\n\n  toCVS(shapes) {\n    return shapes\n      .map((shape) => {\n        if (shape instanceof Circle) {\n          return `Circle,${shape.getRadius()}`;\n        } else if (shape instanceof Square) {\n          return `Square,${shape.getLength()}`;\n        }\n      })\n      .join(\"\\n\");\n  }\n}\n", "import AreaCalculator from \"./areaCalcualtor\";\nimport Circle from \"./shapes/circle\";\nimport Formatter from \"./formater\";\nimport Square from \"./shapes/square\";\n\nconst areaCalcualtor = new AreaCalculator();\nconst circle = new Circle(10);\nconst square = new Square(10);\nconst shapes = [circle, square];\nconst formater = new Formatter();\nconsole.log(formater.toCVS(shapes));\nconsole.log(formater.toJson(shapes));\nconst sum = areaCalcualtor.sum(shapes);\nconsole.log(sum);\n"], "mappings": ";;;AAAA,IAAqB,iBAArB,MAAoC;AAAA,EAClC,IAAIA,SAAQ;AACV,WAAOA,QAAO,OAAO,CAAC,GAAG,MAAM;AAC7B,aAAO,IAAI,EAAE,KAAK;AAAA,IACpB,GAAG,CAAC;AAAA,EACN;AACF;;;ACNA,IAAqB,QAArB,MAA2B;AAAA,EACzB,OAAO;AACL,WAAO;AAAA,EACT;AACF;;;ACFA,IAAqB,SAArB,cAAoC,MAAM;AAAA,EACxC,YAAY,QAAQ;AAClB,UAAM;AACN,SAAK,SAAS,IAAI;AAAA,EACpB;AAAA,EAEA,YAAY;AACV,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EAEA,OAAO;AACL,WAAO,KAAK,KAAK,KAAK,UAAU,IAAI,KAAK,UAAU;AAAA,EACrD;AACF;;;ACbA,IAAqB,SAArB,cAAoC,MAAM;AAAA,EACxC,YAAY,QAAQ;AAClB,UAAM;AACN,SAAK,SAAS,IAAI;AAAA,EACpB;AAAA,EAEA,YAAY;AACV,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EAEA,OAAO;AACL,WAAO,KAAK,UAAU,IAAI,KAAK,UAAU;AAAA,EAC3C;AACF;;;ACZA,IAAqB,YAArB,MAA+B;AAAA,EAC7B,OAAOC,SAAQ;AACb,WAAO,KAAK,UAAUA,OAAM;AAAA,EAC9B;AAAA,EAEA,MAAMA,SAAQ;AACZ,WAAOA,QACJ,IAAI,CAAC,UAAU;AACd,UAAI,iBAAiB,QAAQ;AAC3B,eAAO,UAAU,MAAM,UAAU,CAAC;AAAA,MACpC,WAAW,iBAAiB,QAAQ;AAClC,eAAO,UAAU,MAAM,UAAU,CAAC;AAAA,MACpC;AAAA,IACF,CAAC,EACA,KAAK,IAAI;AAAA,EACd;AACF;;;ACdA,IAAM,iBAAiB,IAAI,eAAe;AAC1C,IAAM,SAAS,IAAI,OAAO,EAAE;AAC5B,IAAM,SAAS,IAAI,OAAO,EAAE;AAC5B,IAAM,SAAS,CAAC,QAAQ,MAAM;AAC9B,IAAM,WAAW,IAAI,UAAU;AAC/B,QAAQ,IAAI,SAAS,MAAM,MAAM,CAAC;AAClC,QAAQ,IAAI,SAAS,OAAO,MAAM,CAAC;AACnC,IAAM,MAAM,eAAe,IAAI,MAAM;AACrC,QAAQ,IAAI,GAAG;", "names": ["shapes", "shapes"]}