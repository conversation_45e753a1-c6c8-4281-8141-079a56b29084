"use strict";

// src/solid/areaCalcualtor.ts
var AreaCalculator = class {
  sum(shapes2) {
    return shapes2.reduce((a, b) => {
      return a + b.area();
    }, 0);
  }
};

// src/solid/shapes/base/shape.ts
var Shape = class {
  area() {
    return 0;
  }
};

// src/solid/shapes/circle.ts
var Circle = class extends Shape {
  constructor(radius) {
    super();
    this["#radius"] = radius;
  }
  getRadius() {
    return this["#radius"];
  }
  area() {
    return Math.PI * this.getRadius() * this.getRadius();
  }
};

// src/solid/shapes/square.ts
var Square = class extends Shape {
  constructor(length) {
    super();
    this["#length"] = length;
  }
  getLength() {
    return this["#length"];
  }
  area() {
    return this.getLength() * this.getLength();
  }
};

// src/solid/formater.ts
var Formatter = class {
  toJson(shapes2) {
    return JSON.stringify(shapes2);
  }
  toCVS(shapes2) {
    return shapes2.map((shape) => {
      if (shape instanceof Circle) {
        return `Circle,${shape.getRadius()}`;
      } else if (shape instanceof Square) {
        return `Square,${shape.getLength()}`;
      }
    }).join("\n");
  }
};

// src/solid/main.ts
var areaCalcualtor = new AreaCalculator();
var circle = new Circle(10);
var square = new Square(10);
var shapes = [circle, square];
var formater = new Formatter();
console.log(formater.toCVS(shapes));
console.log(formater.toJson(shapes));
var sum = areaCalcualtor.sum(shapes);
console.log(sum);
//# sourceMappingURL=index.js.map
